# Video Compression Workflow - Updated Options

## 🎯 Overview

The video compression workflow now has two separate, independent options for handling compressed videos:

1. **`UploadToGooglePhotos`** - Whether to upload the compressed video back to Google Photos
2. **`OverwriteOriginal`** - Whether to delete the original video after uploading the compressed version

## 📋 Option Combinations

| UploadToGooglePhotos | OverwriteOriginal | Result |
|---------------------|-------------------|---------|
| ✅ `true` | ❌ `false` | Upload compressed video, keep original (default) |
| ✅ `true` | ✅ `true` | Upload compressed video, delete original |
| ❌ `false` | ❌ `false` | Keep compressed video in cloud storage only |
| ❌ `false` | ✅ `true` | Invalid - can't delete original without uploading |

## 🔄 Updated Workflow

### **1. User Request**
```json
{
  "quality": "720p",
  "uploadToGooglePhotos": true,    // Default: true
  "overwriteOriginal": false       // Default: false
}
```

### **2. Job Processing Steps**

#### **Always Executed:**
1. `Queued` → Download from Google Photos
2. `DownloadingFromGooglePhotos` → Upload to Cloud Storage
3. `UploadingToStorage` → Start transcoding
4. `TranscodingInProgress` → Wait for completion
5. `DownloadingFromStorage` → Download compressed video

#### **Conditional Steps:**

**If `UploadToGooglePhotos = true`:**
6. `UploadingToGooglePhotos` → Upload to Google Photos

**If `OverwriteOriginal = true` AND `UploadToGooglePhotos = true`:**
7. `DeletingOriginal` → Delete original from Google Photos

**Always:**
8. `Completed` → Job finished

### **3. Status Flow Examples**

#### **Default Behavior (Upload, Keep Original):**
```
Queued → DownloadingFromGooglePhotos → UploadingToStorage → 
TranscodingInProgress → DownloadingFromStorage → 
UploadingToGooglePhotos → Completed
```

#### **Replace Original:**
```
Queued → DownloadingFromGooglePhotos → UploadingToStorage → 
TranscodingInProgress → DownloadingFromStorage → 
UploadingToGooglePhotos → DeletingOriginal → Completed
```

#### **Cloud Storage Only:**
```
Queued → DownloadingFromGooglePhotos → UploadingToStorage → 
TranscodingInProgress → DownloadingFromStorage → Completed
```

## 🗄️ Database Schema

### **CompressionJob Model:**
```csharp
public class CompressionJob
{
    // ... other properties ...
    
    /// <summary>
    /// Whether to upload the compressed video back to Google Photos
    /// </summary>
    public bool UploadToGooglePhotos { get; set; } = true;
    
    /// <summary>
    /// Whether to delete the original video from Google Photos after successful compression and upload
    /// Only applies if UploadToGooglePhotos is true
    /// </summary>
    public bool OverwriteOriginal { get; set; } = false;
}
```

### **User Preferences:**
```csharp
public class User
{
    // ... other properties ...
    
    /// <summary>
    /// Default setting for whether to upload compressed videos back to Google Photos
    /// </summary>
    public bool DefaultUploadToGooglePhotos { get; set; } = true;
    
    /// <summary>
    /// Default setting for whether to delete original videos after compression
    /// </summary>
    public bool DefaultOverwriteOriginal { get; set; } = false;
}
```

## 🎛️ Frontend UI Implications

### **Compression Options UI:**
```
┌─ Compression Settings ─────────────────┐
│                                        │
│ Quality: [720p ▼]                      │
│                                        │
│ ☑️ Upload compressed video to Google    │
│    Photos                              │
│                                        │
│ ☐ Replace original video               │
│   (only if uploading to Google Photos) │
│                                        │
│ [Compress Videos]                      │
└────────────────────────────────────────┘
```

### **Logic:**
- "Replace original" checkbox should be disabled if "Upload to Google Photos" is unchecked
- Default values come from user preferences
- Clear messaging about what each option does

## 🔧 Implementation Notes

### **Backend Logic:**
1. Validate that `OverwriteOriginal` is only `true` if `UploadToGooglePhotos` is `true`
2. Use user's default preferences when creating new jobs
3. Update job status appropriately based on selected options
4. Skip upload/delete steps when not needed

### **Error Handling:**
- If upload to Google Photos fails, don't attempt to delete original
- Provide clear error messages for each step
- Allow retry of individual steps

### **Performance:**
- Skip unnecessary steps to reduce processing time
- Clean up cloud storage files after successful completion
- Implement proper timeout handling for each step

## 📊 Benefits

1. **Flexibility**: Users can choose exactly what they want
2. **Safety**: Default behavior keeps originals safe
3. **Efficiency**: Skip unnecessary upload steps when not needed
4. **Clear Intent**: Separate concerns make the workflow easier to understand
5. **User Control**: Granular control over the compression process
