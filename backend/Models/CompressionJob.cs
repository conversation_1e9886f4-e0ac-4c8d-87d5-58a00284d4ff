using System.ComponentModel.DataAnnotations;

namespace VidCompressor.Models;

public class CompressionJob
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    [Required]
    public string MediaItemId { get; set; } = string.Empty;

    /// <summary>
    /// Base URL from PhotosPicker API for downloading the media item
    /// </summary>
    public string? BaseUrl { get; set; }
    
    [Required]
    public string Quality { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether to upload the compressed video back to Google Photos
    /// </summary>
    public bool UploadToGooglePhotos { get; set; } = true;

    /// <summary>
    /// Whether to delete the original video from Google Photos after successful compression and upload
    /// Only applies if UploadToGooglePhotos is true
    /// </summary>
    public bool OverwriteOriginal { get; set; } = false;
    
    public CompressionJobStatus Status { get; set; } = CompressionJobStatus.Queued;
    
    public string? InputStoragePath { get; set; }
    
    public string? OutputStoragePath { get; set; }
    
    public string? TranscoderJobName { get; set; }
    
    public string? ErrorMessage { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? StartedAt { get; set; }
    
    public DateTime? CompletedAt { get; set; }
    
    public long? OriginalSizeBytes { get; set; }
    
    public long? CompressedSizeBytes { get; set; }
    
    public double? CompressionRatio { get; set; }

    /// <summary>
    /// Original video width in pixels
    /// </summary>
    public int? OriginalWidth { get; set; }

    /// <summary>
    /// Original video height in pixels
    /// </summary>
    public int? OriginalHeight { get; set; }

    // Navigation property
    public User? User { get; set; }
}

public enum CompressionJobStatus
{
    Queued,
    DownloadingFromGooglePhotos,
    UploadingToStorage,
    TranscodingInProgress,
    DownloadingFromStorage,
    UploadingToGooglePhotos,    // Only if UploadToGooglePhotos = true
    DeletingOriginal,           // Only if OverwriteOriginal = true
    Completed,
    Failed,
    Cancelled
}

public class CompressionJobRequest
{
    [Required]
    public string Quality { get; set; } = "medium";

    /// <summary>
    /// Whether to upload the compressed video back to Google Photos (default: true)
    /// </summary>
    public bool UploadToGooglePhotos { get; set; } = true;

    /// <summary>
    /// Whether to delete the original video from Google Photos after successful compression and upload
    /// Only applies if UploadToGooglePhotos is true (default: false)
    /// </summary>
    public bool OverwriteOriginal { get; set; } = false;

    /// <summary>
    /// Base URL from PhotosPicker API for downloading the media item
    /// </summary>
    public string? BaseUrl { get; set; }

    /// <summary>
    /// Original video width in pixels (from PhotosPicker metadata)
    /// </summary>
    public int? OriginalWidth { get; set; }

    /// <summary>
    /// Original video height in pixels (from PhotosPicker metadata)
    /// </summary>
    public int? OriginalHeight { get; set; }
}

public class CompressionJobResponse
{
    public string JobId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public double? CompressionRatio { get; set; }
    public string? ErrorMessage { get; set; }
}
